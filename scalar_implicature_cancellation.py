#!/usr/bin/env python3
"""
标量蕴含取消测试 - 基于惊讶度分析
通过计算"取消成本"来量化评估大型语言模型对语用蕴含的理解程度
"""

import pandas as pd
import torch
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer, AutoModel
from datetime import datetime
import os
import sys
from tqdm import tqdm

# 获取设备数量
num_devices = torch.cuda.device_count()
print("GPU 设备数量: ", num_devices)
# 打印所有设备名称和信息
for i in range(num_devices):
    print(torch.cuda.get_device_properties(i))

# 定义logsoftmax用于从scores获取logprobs
logsoftmax = torch.nn.LogSoftmax(dim=-1)

def getContinuationSurprisal(context, continuation, model, tokenizer, model_name=''):
    """
    计算在给定上下文条件下，continuation部分的惊讶度
    
    Args:
        context: 上下文句子
        continuation: 延续句子
        model: 语言模型
        tokenizer: 分词器
        model_name: 模型名称
        
    Returns:
        float: continuation部分的惊讶度 (-1 * avg_log_probability)
    """
    
    # 构建完整句子
    full_sentence = context + continuation
    
    # 特殊处理ChatGLM模型
    if "chatglm" in model_name.lower():
        # ChatGLM使用特殊的对话格式
        formatted_full = f"[Round 1]\n\n问：请完成这句话：{context}\n\n答：{full_sentence}"
        formatted_context = f"[Round 1]\n\n问：请完成这句话：{context}\n\n答：{context}"
        
        full_input_ids = tokenizer.encode(formatted_full, return_tensors="pt").to("cuda:0" if torch.cuda.is_available() else "cpu")
        context_input_ids = tokenizer.encode(formatted_context, return_tensors="pt")
        
    else:
        # 对于其他模型，直接tokenize
        full_input_ids = tokenizer.encode(full_sentence, return_tensors="pt").to("cuda:0" if torch.cuda.is_available() else "cpu")
        context_input_ids = tokenizer.encode(context, return_tensors="pt")
    
    # 通过模型获取logits
    with torch.no_grad():
        outputs = model(full_input_ids)
    
    # 将logits转换为对数概率
    log_probs = logsoftmax(outputs.logits[0][:-1])
    
    # 获取实际token的对数概率
    input_ids_shifted = full_input_ids[:, 1:].squeeze().unsqueeze(-1)
    
    # 在正确的token位置检索对数概率
    token_log_probs = torch.gather(
        log_probs, 
        dim=-1, 
        index=input_ids_shifted
    ).flatten()
    
    # 计算continuation部分的起始位置
    if "chatglm" in model_name.lower():
        # 对于ChatGLM，需要找到context在完整序列中的结束位置
        context_length = len(context_input_ids[0])
        # 从context结束位置开始计算continuation的概率
        continuation_start_pos = context_length - 1
    else:
        # 对于其他模型，直接使用context的长度
        context_length = len(context_input_ids[0])
        continuation_start_pos = context_length - 1
    
    # 获取continuation部分的对数概率
    continuation_log_probs = token_log_probs[continuation_start_pos:]
    
    # 计算平均对数概率
    avg_log_prob = torch.mean(continuation_log_probs).item()
    
    # 计算惊讶度 (surprisal = -1 * avg_log_probability)
    surprisal = -1 * avg_log_prob
    
    return surprisal

class ScalarImplicatureCancellationTest:
    def __init__(self, model_path, model_name="auto"):
        """
        初始化标量蕴含取消测试类
        
        Args:
            model_path: 模型的本地路径
            model_name: 模型名称，用于识别模型类型和生成文件名
        """
        self.model_path = model_path
        self.model_name = model_name if model_name != "auto" else self._detect_model_name(model_path)
        self.model = None
        self.tokenizer = None
        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        
    def _detect_model_name(self, model_path):
        """从模型路径自动检测模型名称"""
        path_lower = model_path.lower()
        
        # 常见模型名称检测
        if "qwen" in path_lower:
            if "3-4b" in path_lower or "3_4b" in path_lower:
                return "Qwen3-4B"
            elif "2.5" in path_lower:
                return "Qwen2.5"
            elif "2-" in path_lower or "2_" in path_lower:
                return "Qwen2"
            else:
                return "Qwen"
        elif "llama" in path_lower:
            if "3.1" in path_lower:
                return "Llama3.1"
            elif "3" in path_lower:
                return "Llama3"
            else:
                return "Llama"
        elif "chatglm" in path_lower:
            return "ChatGLM"
        elif "baichuan" in path_lower:
            return "Baichuan"
        elif "internlm" in path_lower:
            return "InternLM"
        else:
            # 从路径中提取最后一个文件夹名作为模型名
            return os.path.basename(model_path.rstrip('/'))
    
    def load_model(self):
        """加载模型和tokenizer"""
        print(f"正在从 {self.model_path} 加载 {self.model_name} 模型...")
        
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
        
        try:
            # 特殊处理ChatGLM模型
            if "chatglm" in self.model_name.lower():
                print("检测到ChatGLM模型，使用原生加载方式...")
                try:
                    # 直接导入ChatGLM的类
                    import sys
                    sys.path.append(self.model_path)
                    
                    from modeling_chatglm import ChatGLMForConditionalGeneration
                    from tokenization_chatglm import ChatGLMTokenizer
                    
                    print("正在加载ChatGLM tokenizer...")
                    self.tokenizer = ChatGLMTokenizer.from_pretrained(self.model_path)
                    print("ChatGLM Tokenizer加载成功!")
                    
                    print("正在加载ChatGLM模型...")
                    self.model = ChatGLMForConditionalGeneration.from_pretrained(
                        self.model_path,
                        torch_dtype=torch.float16
                    ).to(self.device)
                    print("ChatGLM模型加载成功!")
                    return
                    
                except Exception as chatglm_e:
                    print(f"ChatGLM原生加载失败: {chatglm_e}")
                    print("尝试使用AutoModel方式...")
                    
                    # 尝试使用AutoModel
                    try:
                        self.tokenizer = AutoTokenizer.from_pretrained(
                            self.model_path,
                            trust_remote_code=True,
                            use_fast=False
                        )
                        print("ChatGLM Tokenizer (Auto模式) 加载成功!")
                        
                        # 使用AutoModel而不是AutoModelForCausalLM
                        self.model = AutoModel.from_pretrained(
                            self.model_path,
                            torch_dtype=torch.float16,
                            trust_remote_code=True
                        ).to(self.device)
                        print("ChatGLM模型 (Auto模式) 加载成功!")
                        return
                    except Exception as chatglm_e2:
                        print(f"ChatGLM Auto模式也失败: {chatglm_e2}")
                        print("尝试标准加载方式...")
            
            # 标准加载方式
            print("正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                local_files_only=True
            )
            print("Tokenizer加载成功!")
            
            # 加载模型
            print("正在加载模型...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                device_map='auto',
                torch_dtype=torch.float16,
                trust_remote_code=True,
                local_files_only=True
            )
            print("模型加载成功!")
            
        except ImportError as e:
            if "sentencepiece" in str(e):
                print(f"错误: 缺少sentencepiece依赖")
                print("请运行: pip install sentencepiece")
                print("或者: conda install -c conda-forge sentencepiece")
                print("或者尝试使用其他模型（如Qwen系列）")
                raise
            else:
                print(f"导入错误: {e}")
                raise
        except Exception as e:
            print(f"模型加载失败: {e}")
            print("尝试使用不同的加载参数...")
            
            # 尝试不同的加载方式
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    trust_remote_code=True
                )
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                ).to(self.device)
                print("使用备用方式加载成功!")
            except Exception as e2:
                print(f"备用加载方式也失败: {e2}")
                raise
        
        self.model.eval()
        
        print(f"模型加载完成! 设备: {self.device}")
        print(f"模型数据类型: {self.model.dtype}")
    
    def calculate_cancellation_cost(self, context, continuation_confirm, continuation_cancel):
        """
        计算取消成本 (cancellation_cost = surprisal_cancel - surprisal_confirm)
        
        Args:
            context: 上下文句子
            continuation_confirm: 确认标量蕴含的延续句
            continuation_cancel: 取消标量蕴含的延续句
            
        Returns:
            dict: 包含惊讶度分析结果的字典
        """
        
        print(f"\n分析句子:")
        print(f"  上下文: {context}")
        print(f"  确认延续: {continuation_confirm}")
        print(f"  取消延续: {continuation_cancel}")
        
        # 计算确认延续的惊讶度
        surprisal_confirm = getContinuationSurprisal(
            context,
            continuation_confirm,
            self.model,
            self.tokenizer,
            model_name=self.model_name
        )
        
        # 计算取消延续的惊讶度
        surprisal_cancel = getContinuationSurprisal(
            context,
            continuation_cancel,
            self.model,
            self.tokenizer,
            model_name=self.model_name
        )
        
        # 计算取消成本
        cancellation_cost = surprisal_cancel - surprisal_confirm
        
        print(f"  确认延续惊讶度: {surprisal_confirm:.6f}")
        print(f"  取消延续惊讶度: {surprisal_cancel:.6f}")
        print(f"  取消成本: {cancellation_cost:.6f}")
        
        return {
            "context": context,
            "continuation_confirm": continuation_confirm,
            "continuation_cancel": continuation_cancel,
            "surprisal_confirm": surprisal_confirm,
            "surprisal_cancel": surprisal_cancel,
            "cancellation_cost": cancellation_cost
        }

    def process_csv(self, csv_path):
        """
        处理CSV文件中的句子，计算取消成本

        Args:
            csv_path: CSV文件路径

        Returns:
            list: 包含所有结果的列表
        """

        print(f"正在读取CSV文件: {csv_path}")

        # 读取CSV文件
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
            print(f"成功读取 {len(df)} 行数据")
        except Exception as e:
            print(f"读取CSV文件失败: {e}")
            raise

        # 验证列名
        required_columns = ['context', 'continuation_confirm', 'continuation_cancel']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"CSV文件缺少必需的列: {missing_columns}")

        results = []

        print(f"\n开始处理 {len(df)} 个句子...")
        print("=" * 60)

        # 处理每一行数据
        for index, row in tqdm(df.iterrows(), total=len(df), desc="计算取消成本"):
            try:
                context = str(row['context']).strip()
                continuation_confirm = str(row['continuation_confirm']).strip()
                continuation_cancel = str(row['continuation_cancel']).strip()

                # 跳过空行
                if not context or not continuation_confirm or not continuation_cancel:
                    print(f"跳过第 {index + 1} 行: 包含空值")
                    continue

                if context == 'nan' or continuation_confirm == 'nan' or continuation_cancel == 'nan':
                    print(f"跳过第 {index + 1} 行: 包含空值")
                    continue

                # 计算取消成本
                result = self.calculate_cancellation_cost(context, continuation_confirm, continuation_cancel)
                result['row_index'] = index + 1
                results.append(result)

            except Exception as e:
                print(f"处理第 {index + 1} 行时出错: {e}")
                # 记录错误但继续处理
                error_result = {
                    "context": str(row.get('context', '')),
                    "continuation_confirm": str(row.get('continuation_confirm', '')),
                    "continuation_cancel": str(row.get('continuation_cancel', '')),
                    "surprisal_confirm": None,
                    "surprisal_cancel": None,
                    "cancellation_cost": None,
                    "row_index": index + 1,
                    "error": str(e)
                }
                results.append(error_result)

        print("=" * 60)
        print(f"处理完成! 成功处理 {len([r for r in results if 'error' not in r])} 个句子")

        return results

    def save_results(self, results, output_path=None):
        """
        保存取消成本分析结果到CSV文件

        Args:
            results: 结果列表
            output_path: 输出文件路径（可选）
        """

        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"results_cancellation_{self.model_name}_{timestamp}.csv"

        # 转换为DataFrame
        df_results = pd.DataFrame(results)

        # 重新排列列的顺序
        column_order = [
            'row_index', 'context', 'continuation_confirm', 'continuation_cancel',
            'surprisal_confirm', 'surprisal_cancel', 'cancellation_cost'
        ]

        # 添加error列（如果存在）
        if 'error' in df_results.columns:
            column_order.append('error')

        # 只保留存在的列
        existing_columns = [col for col in column_order if col in df_results.columns]
        df_results = df_results[existing_columns]

        # 保存到CSV
        df_results.to_csv(output_path, index=False, encoding='utf-8')

        print(f"\n结果已保存到: {output_path}")

        # 计算统计信息
        valid_results = df_results[df_results['cancellation_cost'].notna()]
        if len(valid_results) > 0:
            mean_cost = valid_results['cancellation_cost'].mean()
            std_cost = valid_results['cancellation_cost'].std()
            min_cost = valid_results['cancellation_cost'].min()
            max_cost = valid_results['cancellation_cost'].max()

            # 计算正取消成本的比例（理论上应该为正）
            positive_costs = valid_results[valid_results['cancellation_cost'] > 0]
            positive_ratio = len(positive_costs) / len(valid_results) * 100

            print(f"\n取消成本统计:")
            print(f"  平均值: {mean_cost:.6f}")
            print(f"  标准差: {std_cost:.6f}")
            print(f"  最小值: {min_cost:.6f}")
            print(f"  最大值: {max_cost:.6f}")
            print(f"  正值比例: {positive_ratio:.1f}% ({len(positive_costs)}/{len(valid_results)})")
            print(f"  有效样本数: {len(valid_results)}")

            # 理论解释
            print(f"\n理论解释:")
            print(f"  - 取消成本 > 0: 模型对取消标量蕴含感到更惊讶（符合预期）")
            print(f"  - 取消成本 < 0: 模型对确认标量蕴含感到更惊讶（不符合预期）")
            print(f"  - 正值比例越高，说明模型对标量蕴含的理解越好")

        return output_path

def get_model_selection():
    """获取用户的模型选择"""
    print("=" * 60)
    print("    标量蕴含取消测试 - 惊讶度分析")
    print("=" * 60)

    print("\n请输入要测试的模型信息:")

    # 直接让用户输入模型路径
    model_path = input("模型路径: ").strip()

    # 让用户输入模型名称（可选）
    model_name = input("模型名称 (用于文件命名，留空自动检测): ").strip()

    if not model_name:
        model_name = "auto"

    return model_path, model_name

def main():
    """主函数"""

    # 可配置参数
    csv_path = "cancellation_test.csv"  # 输入CSV文件路径

    try:
        # 检查输入文件是否存在
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"输入CSV文件不存在: {csv_path}")

        # 获取模型选择
        model_path, model_name = get_model_selection()

        # 检查模型路径是否存在
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型路径不存在: {model_path}")

        print(f"\n使用模型: {model_name}")
        print(f"模型路径: {model_path}")
        print(f"输入文件: {csv_path}")
        print("-" * 60)

        # 创建测试实例
        test = ScalarImplicatureCancellationTest(
            model_path=model_path,
            model_name=model_name
        )

        # 加载模型
        test.load_model()

        # 处理CSV文件
        results = test.process_csv(csv_path)

        # 保存结果
        output_path = test.save_results(results)

        print(f"\n🎉 标量蕴含取消测试完成!")
        print(f"使用模型: {test.model_name}")
        print(f"结果文件: {output_path}")

    except Exception as e:
        print(f"运行时出错: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    main()
