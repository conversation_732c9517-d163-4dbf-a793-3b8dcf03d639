#!/usr/bin/env python3
"""
标量蕴含取消测试 - 基于惊讶度分析

通过计算"取消成本"来量化评估大型语言模型对语用蕴含的理解程度。

理论背景:
- 标量蕴含：从"一些"推断出"不是所有"
- 取消成本：模型对取消标量蕴含的惊讶度 - 对确认标量蕴含的惊讶度
- 预期：取消成本应为正值，表明模型理解语用推理

作者: [Your Name]
日期: 2025-01-05
"""

import pandas as pd
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from datetime import datetime
import os
from tqdm import tqdm
# ================================
# 全局配置和初始化
# ================================

def initialize_environment() -> None:
    """初始化运行环境，检查GPU设备"""
    num_devices = torch.cuda.device_count()
    print(f"🔧 GPU 设备数量: {num_devices}")

    if num_devices > 0:
        print("📱 可用GPU设备:")
        for i in range(num_devices):
            props = torch.cuda.get_device_properties(i)
            print(f"  - GPU {i}: {props.name} ({props.total_memory // 1024**3}GB)")
    else:
        print("⚠️  未检测到GPU设备，将使用CPU运行")
    print("-" * 60)

# 定义logsoftmax用于从scores获取logprobs
logsoftmax = torch.nn.LogSoftmax(dim=-1)

# ================================
# 核心计算函数
# ================================

def getContinuationSurprisal(
    context: str,
    continuation: str,
    model: AutoModelForCausalLM,
    tokenizer: AutoTokenizer
) -> float:
    """
    计算在给定上下文条件下，continuation部分的惊讶度

    惊讶度 (Surprisal) = -log P(continuation | context)
    值越高表示模型对该延续越"惊讶"

    Args:
        context: 上下文句子
        continuation: 延续句子
        model: 预训练语言模型
        tokenizer: 对应的分词器

    Returns:
        float: continuation部分的惊讶度 (-1 * avg_log_probability)

    Raises:
        RuntimeError: 当模型推理失败时
    """
    try:
        # 构建完整句子
        full_sentence = context + continuation
        device = next(model.parameters()).device

        # Tokenize输入
        full_input_ids = tokenizer.encode(
            full_sentence,
            return_tensors="pt"
        ).to(device)
        context_input_ids = tokenizer.encode(context, return_tensors="pt")

        # 模型前向传播
        with torch.no_grad():
            outputs = model(full_input_ids)

        # 计算对数概率
        log_probs = logsoftmax(outputs.logits[0][:-1])

        # 获取实际token的对数概率
        input_ids_shifted = full_input_ids[:, 1:].squeeze().unsqueeze(-1)
        token_log_probs = torch.gather(
            log_probs,
            dim=-1,
            index=input_ids_shifted
        ).flatten()

        # 定位continuation部分
        context_length = len(context_input_ids[0])
        continuation_start_pos = context_length - 1
        continuation_log_probs = token_log_probs[continuation_start_pos:]

        # 计算平均对数概率和惊讶度
        avg_log_prob = torch.mean(continuation_log_probs).item()
        surprisal = -1 * avg_log_prob

        return surprisal

    except Exception as e:
        raise RuntimeError(f"计算惊讶度时出错: {e}") from e

# ================================
# 主要测试类
# ================================

class ScalarImplicatureCancellationTest:
    """
    标量蕴含取消测试类

    用于评估语言模型对标量蕴含的理解能力。通过比较模型对
    "确认标量蕴含"和"取消标量蕴含"的惊讶度来量化评估。
    """

    def __init__(self, model_path: str, model_name: str = "auto") -> None:
        """
        初始化测试实例

        Args:
            model_path: 模型的本地路径
            model_name: 模型名称，用于生成输出文件名
        """
        self.model_path = model_path
        self.model_name = (
            model_name if model_name != "auto"
            else self._detect_model_name(model_path)
        )
        self.model: AutoModelForCausalLM = None
        self.tokenizer: AutoTokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

    def _detect_model_name(self, model_path: str) -> str:
        """从模型路径自动检测模型名称"""
        return os.path.basename(model_path.rstrip('/')) or "unknown_model"
    
    def load_model(self) -> None:
        """
        加载预训练模型和分词器

        Raises:
            FileNotFoundError: 当模型路径不存在时
            ImportError: 当缺少必要依赖时
            RuntimeError: 当模型加载失败时
        """
        print(f"🚀 正在从 {self.model_path} 加载 {self.model_name} 模型...")

        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"❌ 模型路径不存在: {self.model_path}")

        try:
            # 加载分词器
            print("📝 正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                local_files_only=True
            )
            print("✅ Tokenizer加载成功!")

            # 加载模型
            print("🧠 正在加载模型...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                device_map='auto',
                torch_dtype=torch.float16,
                trust_remote_code=True,
                local_files_only=True
            )
            print("✅ 模型加载成功!")

        except ImportError as e:
            if "sentencepiece" in str(e):
                error_msg = (
                    "❌ 缺少sentencepiece依赖\n"
                    "请运行以下命令之一:\n"
                    "  pip install sentencepiece\n"
                    "  conda install -c conda-forge sentencepiece"
                )
                print(error_msg)
                raise ImportError(error_msg) from e
            else:
                raise ImportError(f"导入错误: {e}") from e

        except Exception as e:
            print(f"⚠️  标准加载失败: {e}")
            print("🔄 尝试备用加载方式...")

            # 备用加载方式
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    trust_remote_code=True
                )
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                ).to(self.device)
                print("✅ 备用方式加载成功!")
            except Exception as e2:
                raise RuntimeError(f"所有加载方式都失败: {e2}") from e

        # 设置为评估模式
        self.model.eval()

        print(f"🎯 模型加载完成!")
        print(f"   设备: {self.device}")
        print(f"   数据类型: {self.model.dtype}")
        print("-" * 60)
    
    def calculate_cancellation_cost(
        self,
        context: str,
        continuation_confirm: str,
        continuation_cancel: str
    ) -> dict:
        """
        计算标量蕴含的取消成本

        取消成本 = 取消延续的惊讶度 - 确认延续的惊讶度

        理论预期:
        - 取消成本 > 0: 模型对取消标量蕴含更惊讶（符合语用学预期）
        - 取消成本 < 0: 模型对确认标量蕴含更惊讶（不符合预期）

        Args:
            context: 上下文句子
            continuation_confirm: 确认标量蕴含的延续句
            continuation_cancel: 取消标量蕴含的延续句

        Returns:
            dict: 包含完整分析结果的字典
        """

        print(f"\n🔍 分析句子:")
        print(f"   上下文: {context}")
        print(f"   确认延续: {continuation_confirm}")
        print(f"   取消延续: {continuation_cancel}")

        # 计算两种延续的惊讶度
        surprisal_confirm = getContinuationSurprisal(
            context, continuation_confirm, self.model, self.tokenizer
        )
        surprisal_cancel = getContinuationSurprisal(
            context, continuation_cancel, self.model, self.tokenizer
        )

        # 计算取消成本
        cancellation_cost = surprisal_cancel - surprisal_confirm

        # 输出结果
        print(f"   📊 确认延续惊讶度: {surprisal_confirm:.6f}")
        print(f"   📊 取消延续惊讶度: {surprisal_cancel:.6f}")
        print(f"   💰 取消成本: {cancellation_cost:.6f}")

        # 解释结果
        if cancellation_cost > 0:
            print(f"   ✅ 结果符合预期（取消成本 > 0）")
        else:
            print(f"   ⚠️  结果不符合预期（取消成本 ≤ 0）")

        return {
            "context": context,
            "continuation_confirm": continuation_confirm,
            "continuation_cancel": continuation_cancel,
            "surprisal_confirm": surprisal_confirm,
            "surprisal_cancel": surprisal_cancel,
            "cancellation_cost": cancellation_cost
        }

    def process_csv(self, csv_path: str) -> list:
        """
        批量处理CSV文件中的测试数据

        读取CSV文件中的每一行，计算标量蕴含取消成本，
        并处理可能出现的错误情况。

        Args:
            csv_path: CSV文件路径

        Returns:
            list: 包含所有分析结果的列表

        Raises:
            FileNotFoundError: 当CSV文件不存在时
            ValueError: 当CSV文件格式不正确时
        """

        print(f"📂 正在读取CSV文件: {csv_path}")

        # 读取和验证CSV文件
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
            print(f"✅ 成功读取 {len(df)} 行数据")
        except Exception as e:
            raise FileNotFoundError(f"读取CSV文件失败: {e}") from e

        # 验证必需的列
        required_columns = ['context', 'continuation_confirm', 'continuation_cancel']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"CSV文件缺少必需的列: {missing_columns}")

        print(f"📊 数据列验证通过: {required_columns}")

        results = []
        successful_count = 0
        error_count = 0

        print(f"\n🚀 开始处理 {len(df)} 个测试样本...")
        print("=" * 60)

        # 批量处理数据
        for index, row in tqdm(df.iterrows(), total=len(df), desc="🔄 计算取消成本"):
            try:
                # 提取和清理数据
                context = str(row['context']).strip()
                continuation_confirm = str(row['continuation_confirm']).strip()
                continuation_cancel = str(row['continuation_cancel']).strip()

                # 数据有效性检查
                if self._is_invalid_data(context, continuation_confirm, continuation_cancel):
                    print(f"⚠️  跳过第 {index + 1} 行: 包含无效数据")
                    continue

                # 计算取消成本
                result = self.calculate_cancellation_cost(
                    context, continuation_confirm, continuation_cancel
                )
                result['row_index'] = index + 1
                results.append(result)
                successful_count += 1

            except Exception as e:
                print(f"❌ 处理第 {index + 1} 行时出错: {e}")
                error_count += 1

                # 记录错误但继续处理
                error_result = {
                    "context": str(row.get('context', '')),
                    "continuation_confirm": str(row.get('continuation_confirm', '')),
                    "continuation_cancel": str(row.get('continuation_cancel', '')),
                    "surprisal_confirm": None,
                    "surprisal_cancel": None,
                    "cancellation_cost": None,
                    "row_index": index + 1,
                    "error": str(e)
                }
                results.append(error_result)

        # 输出处理统计
        print("=" * 60)
        print(f"📈 处理完成统计:")
        print(f"   ✅ 成功处理: {successful_count} 个样本")
        print(f"   ❌ 处理失败: {error_count} 个样本")
        print(f"   📊 总计: {len(results)} 个结果")

        return results

    def _is_invalid_data(self, context: str, confirm: str, cancel: str) -> bool:
        """检查数据是否有效"""
        invalid_values = ['', 'nan', 'None', 'null']
        return (
            not context or not confirm or not cancel or
            context in invalid_values or confirm in invalid_values or cancel in invalid_values
        )

    def save_results(self, results: list, output_path: str = None) -> str:
        """
        保存分析结果并生成统计报告

        将测试结果保存为CSV文件，并计算详细的统计信息，
        包括取消成本的分布和模型表现评估。

        Args:
            results: 分析结果列表
            output_path: 输出文件路径（可选，默认自动生成）

        Returns:
            str: 保存的文件路径
        """

        # 生成输出文件名
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"results_cancellation_{self.model_name}_{timestamp}.csv"

        # 数据处理和保存
        df_results = pd.DataFrame(results)

        # 优化列顺序
        column_order = [
            'row_index', 'context', 'continuation_confirm', 'continuation_cancel',
            'surprisal_confirm', 'surprisal_cancel', 'cancellation_cost'
        ]

        if 'error' in df_results.columns:
            column_order.append('error')

        # 重新排列列并保存
        existing_columns = [col for col in column_order if col in df_results.columns]
        df_results = df_results[existing_columns]
        df_results.to_csv(output_path, index=False, encoding='utf-8')

        print(f"\n💾 结果已保存到: {output_path}")

        # 生成详细统计报告
        self._generate_statistics_report(df_results)

        return output_path

    def _generate_statistics_report(self, df_results: pd.DataFrame) -> None:
        """生成详细的统计分析报告"""
        valid_results = df_results[df_results['cancellation_cost'].notna()]

        if len(valid_results) == 0:
            print("⚠️  没有有效的结果数据用于统计分析")
            return

        # 基础统计
        mean_cost = valid_results['cancellation_cost'].mean()
        std_cost = valid_results['cancellation_cost'].std()
        min_cost = valid_results['cancellation_cost'].min()
        max_cost = valid_results['cancellation_cost'].max()
        median_cost = valid_results['cancellation_cost'].median()

        # 正值比例分析
        positive_costs = valid_results[valid_results['cancellation_cost'] > 0]
        negative_costs = valid_results[valid_results['cancellation_cost'] < 0]
        zero_costs = valid_results[valid_results['cancellation_cost'] == 0]

        positive_ratio = len(positive_costs) / len(valid_results) * 100
        negative_ratio = len(negative_costs) / len(valid_results) * 100
        zero_ratio = len(zero_costs) / len(valid_results) * 100

        # 输出统计报告
        print(f"\n📊 取消成本统计分析:")
        print("=" * 50)
        print(f"📈 描述性统计:")
        print(f"   平均值: {mean_cost:.6f}")
        print(f"   中位数: {median_cost:.6f}")
        print(f"   标准差: {std_cost:.6f}")
        print(f"   最小值: {min_cost:.6f}")
        print(f"   最大值: {max_cost:.6f}")
        print(f"   有效样本数: {len(valid_results)}")

        print(f"\n🎯 取消成本分布:")
        print(f"   正值 (> 0): {positive_ratio:.1f}% ({len(positive_costs)} 个)")
        print(f"   负值 (< 0): {negative_ratio:.1f}% ({len(negative_costs)} 个)")
        print(f"   零值 (= 0): {zero_ratio:.1f}% ({len(zero_costs)} 个)")

        # 模型表现评估
        print(f"\n🧠 模型表现评估:")
        if positive_ratio >= 70:
            print(f"   ✅ 优秀: 模型很好地理解了标量蕴含")
        elif positive_ratio >= 50:
            print(f"   🟡 良好: 模型基本理解标量蕴含")
        else:
            print(f"   ❌ 需要改进: 模型对标量蕴含的理解有限")

        print(f"\n📚 理论解释:")
        print(f"   • 取消成本 > 0: 模型对取消标量蕴含更惊讶（符合语用学预期）")
        print(f"   • 取消成本 < 0: 模型对确认标量蕴含更惊讶（不符合预期）")
        print(f"   • 正值比例越高，说明模型对标量蕴含的理解越好")
        print("=" * 50)

# ================================
# 用户交互函数
# ================================

def get_model_selection() -> tuple[str, str]:
    """
    获取用户的模型选择

    Returns:
        tuple: (model_path, model_name)
    """
    print("=" * 60)
    print("🧪 标量蕴含取消测试 - 惊讶度分析")
    print("=" * 60)

    print("\n📝 请输入要测试的模型信息:")

    # 获取模型路径
    while True:
        model_path = input("🔗 模型路径: ").strip()
        if model_path:
            break
        print("❌ 模型路径不能为空，请重新输入")

    # 获取模型名称（可选）
    model_name = input("🏷️  模型名称 (用于文件命名，留空自动检测): ").strip()
    if not model_name:
        model_name = "auto"

    return model_path, model_name

# ================================
# 主程序入口
# ================================

def main() -> None:
    """
    主函数 - 执行完整的标量蕴含取消测试流程

    流程:
    1. 初始化环境
    2. 获取用户输入
    3. 加载模型
    4. 处理测试数据
    5. 保存和分析结果
    """
    # 配置参数
    CSV_PATH = "SD_Exp2.csv"

    try:
        # 初始化环境
        initialize_environment()

        # 检查输入文件
        if not os.path.exists(CSV_PATH):
            raise FileNotFoundError(f"❌ 输入CSV文件不存在: {CSV_PATH}")

        # 获取用户输入
        model_path, model_name = get_model_selection()

        # 验证模型路径
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"❌ 模型路径不存在: {model_path}")

        # 显示配置信息
        print(f"\n📋 测试配置:")
        print(f"   模型名称: {model_name}")
        print(f"   模型路径: {model_path}")
        print(f"   输入文件: {CSV_PATH}")
        print("=" * 60)

        # 创建测试实例并运行
        test = ScalarImplicatureCancellationTest(
            model_path=model_path,
            model_name=model_name
        )

        # 执行测试流程
        test.load_model()
        results = test.process_csv(CSV_PATH)
        output_path = test.save_results(results)

        # 显示完成信息
        print(f"\n🎉 标量蕴含取消测试完成!")
        print(f"   使用模型: {test.model_name}")
        print(f"   结果文件: {output_path}")
        print("=" * 60)

    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断了程序执行")
    except Exception as e:
        print(f"\n❌ 运行时出错: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    main()
